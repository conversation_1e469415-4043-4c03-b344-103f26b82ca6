import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import Sidebar from './Sidebar';
import { router } from 'expo-router';

interface DesktopLayoutProps {
  children: React.ReactNode;
}

export default function DesktopLayout({ children }: DesktopLayoutProps) {
  const handleNavigate = (route: string) => {
    router.push(route);
  };

  if (Platform.OS !== 'web') {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      <Sidebar onNavigate={handleNavigate} />
      <View style={styles.content}>
        {children}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
    overflow: 'hidden',
  },
});