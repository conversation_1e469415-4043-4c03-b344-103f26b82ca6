import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import { Chrome as Home, Building, CreditCard, MessageCircle, User, Bell, Settings, LogOut, ChartBar as BarChart3 } from 'lucide-react-native';
import { useAuthStore } from '@/stores/authStore';
import { router, usePathname } from 'expo-router';

interface SidebarProps {
  onNavigate: (route: string) => void;
}

export default function Sidebar({ onNavigate }: SidebarProps) {
  const { user, logout } = useAuthStore();
  const pathname = usePathname();

  const handleLogout = () => {
    logout();
    router.replace('/(auth)/login');
  };

  const menuItems = [
    { id: 'index', icon: Home, label: 'Dashboard', route: '/(tabs)' },
    { id: 'properties', icon: Building, label: 'Properties', route: '/(tabs)/properties' },
    { id: 'payments', icon: CreditCard, label: 'Payments', route: '/(tabs)/payments' },
    { id: 'analytics', icon: BarChart3, label: 'Analytics', route: '/(tabs)/analytics' },
    { id: 'messages', icon: MessageCircle, label: 'Messages', route: '/(tabs)/messages' },
  ];

  const isActive = (route: string) => {
    if (route === '/(tabs)') {
      return pathname === '/(tabs)' || pathname === '/';
    }
    return pathname.includes(route.split('/').pop() || '');
  };

  if (Platform.OS !== 'web') {
    return null;
  }

  return (
    <View style={styles.sidebar}>
      {/* Logo */}
      <View style={styles.logo}>
        <View style={styles.logoIcon}>
          <Building size={32} color="#FFFFFF" />
        </View>
        <Text style={styles.logoText}>RentFlow</Text>
      </View>

      {/* User Info */}
      <View style={styles.userInfo}>
        <View style={styles.avatar}>
          <User size={24} color="#6B7280" />
        </View>
        <View style={styles.userDetails}>
          <Text style={styles.userName}>
            {user ? `${user.firstName} ${user.lastName}` : 'John Doe'}
          </Text>
          <Text style={styles.userRole}>
            {user?.role === 'landlord' ? 'Property Manager' : 'Tenant'}
          </Text>
        </View>
      </View>

      {/* Navigation Menu */}
      <View style={styles.navigation}>
        <Text style={styles.navSection}>MAIN MENU</Text>
        {menuItems.map((item) => (
          <TouchableOpacity
            key={item.id}
            style={[
              styles.navItem,
              isActive(item.route) && styles.navItemActive
            ]}
            onPress={() => onNavigate(item.route)}
          >
            <item.icon 
              size={20} 
              color={isActive(item.route) ? '#3B82F6' : '#6B7280'} 
            />
            <Text style={[
              styles.navLabel,
              isActive(item.route) && styles.navLabelActive
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Settings Section */}
      <View style={styles.settingsSection}>
        <Text style={styles.navSection}>SETTINGS</Text>
        <TouchableOpacity style={styles.navItem}>
          <Bell size={20} color="#6B7280" />
          <Text style={styles.navLabel}>Notifications</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Settings size={20} color="#6B7280" />
          <Text style={styles.navLabel}>Preferences</Text>
        </TouchableOpacity>
      </View>

      {/* Logout */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <LogOut size={20} color="#EF4444" />
        <Text style={styles.logoutText}>Log Out</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  sidebar: {
    width: 280,
    backgroundColor: '#FFFFFF',
    borderRightWidth: 1,
    borderRightColor: '#E5E7EB',
    paddingVertical: 24,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  logo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 32,
    gap: 12,
  },
  logoIcon: {
    backgroundColor: '#3B82F6',
    padding: 8,
    borderRadius: 12,
  },
  logoText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 32,
    gap: 12,
  },
  avatar: {
    width: 48,
    height: 48,
    backgroundColor: '#E5E7EB',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  userRole: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  navigation: {
    flex: 1,
  },
  navSection: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9CA3AF',
    marginBottom: 16,
    letterSpacing: 0.5,
  },
  navItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 4,
    gap: 12,
  },
  navItemActive: {
    backgroundColor: '#EFF6FF',
  },
  navLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  navLabelActive: {
    color: '#3B82F6',
  },
  settingsSection: {
    marginTop: 32,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 12,
    marginTop: 16,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#EF4444',
  },
});