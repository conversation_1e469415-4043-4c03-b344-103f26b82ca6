import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Dimensions } from 'react-native';
import { Building, User } from 'lucide-react-native';
import { useAuthStore } from '@/stores/authStore';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function RoleToggle() {
  const { user, updateUser } = useAuthStore();
  
  if (!user || isDesktop) return null;

  const toggleRole = () => {
    const newRole = user.role === 'landlord' ? 'tenant' : 'landlord';
    updateUser({ role: newRole });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Demo Mode - Switch Role:</Text>
      <TouchableOpacity style={styles.toggleButton} onPress={toggleRole}>
        {user.role === 'landlord' ? (
          <Building size={20} color="#3B82F6" />
        ) : (
          <User size={20} color="#10B981" />
        )}
        <Text style={styles.toggleText}>
          Currently: {user.role === 'landlord' ? 'Landlord' : 'Tenant'}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F59E0B',
  },
  label: {
    fontSize: 12,
    color: '#92400E',
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 8,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
  },
});