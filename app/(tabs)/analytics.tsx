import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Platform, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TrendingUp, TrendingDown, DollarSign, Users, Building, Calendar, Download, Filter } from 'lucide-react-native';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Analytics() {
  const [selectedPeriod, setSelectedPeriod] = useState('3months');

  const periods = [
    { id: '1month', label: '1 Month' },
    { id: '3months', label: '3 Months' },
    { id: '6months', label: '6 Months' },
    { id: '1year', label: '1 Year' },
  ];

  const kpiCards = [
    {
      title: 'Total Revenue',
      value: '$124,580',
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign,
      color: '#10B981',
    },
    {
      title: 'Occupancy Rate',
      value: '92%',
      change: '+2.1%',
      trend: 'up',
      icon: Users,
      color: '#3B82F6',
    },
    {
      title: 'Properties',
      value: '24',
      change: '+1',
      trend: 'up',
      icon: Building,
      color: '#8B5CF6',
    },
    {
      title: 'Avg. Rent',
      value: '$1,245',
      change: '-1.2%',
      trend: 'down',
      icon: Calendar,
      color: '#F59E0B',
    },
  ];

  // Mock chart data
  const revenueData = [
    { month: 'Jan', revenue: 85000, expenses: 25000 },
    { month: 'Feb', revenue: 92000, expenses: 28000 },
    { month: 'Mar', revenue: 88000, expenses: 26000 },
    { month: 'Apr', revenue: 95000, expenses: 30000 },
    { month: 'May', revenue: 102000, expenses: 32000 },
    { month: 'Jun', revenue: 98000, expenses: 29000 },
  ];

  const occupancyData = [
    { property: '15 Main St', occupancy: 95 },
    { property: 'Riverside Apt', occupancy: 88 },
    { property: 'Oak Hill', occupancy: 92 },
    { property: 'Downtown Plaza', occupancy: 85 },
  ];

  const SimpleChart = ({ data, type }: { data: any[], type: 'revenue' | 'occupancy' }) => {
    if (type === 'revenue') {
      const maxValue = Math.max(...data.map(d => d.revenue));
      return (
        <View style={styles.chartContainer}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Revenue vs Expenses</Text>
            <View style={styles.chartLegend}>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, { backgroundColor: '#3B82F6' }]} />
                <Text style={styles.legendText}>Revenue</Text>
              </View>
              <View style={styles.legendItem}>
                <View style={[styles.legendColor, { backgroundColor: '#EF4444' }]} />
                <Text style={styles.legendText}>Expenses</Text>
              </View>
            </View>
          </View>
          <View style={styles.chart}>
            {data.map((item, index) => (
              <View key={index} style={styles.chartBar}>
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar,
                      { 
                        height: (item.revenue / maxValue) * 120,
                        backgroundColor: '#3B82F6'
                      }
                    ]} 
                  />
                  <View 
                    style={[
                      styles.bar,
                      { 
                        height: (item.expenses / maxValue) * 120,
                        backgroundColor: '#EF4444',
                        marginLeft: 4
                      }
                    ]} 
                  />
                </View>
                <Text style={styles.barLabel}>{item.month}</Text>
              </View>
            ))}
          </View>
        </View>
      );
    }

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Occupancy by Property</Text>
        <View style={styles.occupancyChart}>
          {data.map((item, index) => (
            <View key={index} style={styles.occupancyItem}>
              <Text style={styles.occupancyProperty}>{item.property}</Text>
              <View style={styles.occupancyBarContainer}>
                <View 
                  style={[
                    styles.occupancyBar,
                    { width: `${item.occupancy}%` }
                  ]} 
                />
              </View>
              <Text style={styles.occupancyValue}>{item.occupancy}%</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const content = (
    <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.title}>Analytics Dashboard</Text>
          <Text style={styles.subtitle}>Financial insights and property performance</Text>
        </View>
        <View style={styles.headerActions}>
          <View style={styles.periodSelector}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period.id}
                style={[
                  styles.periodButton,
                  selectedPeriod === period.id && styles.periodButtonActive
                ]}
                onPress={() => setSelectedPeriod(period.id)}
              >
                <Text style={[
                  styles.periodText,
                  selectedPeriod === period.id && styles.periodTextActive
                ]}>
                  {period.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <TouchableOpacity style={styles.exportButton}>
            <Download size={20} color="#3B82F6" />
            <Text style={styles.exportText}>Export</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* KPI Cards */}
      <View style={[styles.kpiGrid, isDesktop && styles.kpiGridDesktop]}>
        {kpiCards.map((kpi, index) => (
          <View key={index} style={[styles.kpiCard, isDesktop && styles.kpiCardDesktop]}>
            <View style={styles.kpiHeader}>
              <View style={[styles.kpiIcon, { backgroundColor: `${kpi.color}20` }]}>
                <kpi.icon size={24} color={kpi.color} />
              </View>
              <View style={[styles.changeIndicator, { backgroundColor: kpi.trend === 'up' ? '#DCFCE7' : '#FEE2E2' }]}>
                {kpi.trend === 'up' ? (
                  <TrendingUp size={16} color="#16A34A" />
                ) : (
                  <TrendingDown size={16} color="#DC2626" />
                )}
                <Text style={[
                  styles.changeText,
                  { color: kpi.trend === 'up' ? '#16A34A' : '#DC2626' }
                ]}>
                  {kpi.change}
                </Text>
              </View>
            </View>
            <Text style={styles.kpiValue}>{kpi.value}</Text>
            <Text style={styles.kpiTitle}>{kpi.title}</Text>
          </View>
        ))}
      </View>

      {/* Charts Section */}
      <View style={[styles.chartsSection, isDesktop && styles.chartsSectionDesktop]}>
        <View style={[styles.chartCard, isDesktop && styles.chartCardDesktop]}>
          <SimpleChart data={revenueData} type="revenue" />
        </View>
        
        <View style={[styles.chartCard, isDesktop && styles.chartCardDesktop]}>
          <SimpleChart data={occupancyData} type="occupancy" />
        </View>
      </View>

      {/* Financial Summary */}
      <View style={styles.summarySection}>
        <Text style={styles.sectionTitle}>Financial Summary</Text>
        <View style={[styles.summaryGrid, isDesktop && styles.summaryGridDesktop]}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Total Income (YTD)</Text>
            <Text style={styles.summaryValue}>$1,247,580</Text>
            <Text style={styles.summaryChange}>+18.2% from last year</Text>
          </View>
          
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Total Expenses (YTD)</Text>
            <Text style={styles.summaryValue}>$347,200</Text>
            <Text style={styles.summaryChange}>+5.1% from last year</Text>
          </View>
          
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Net Profit (YTD)</Text>
            <Text style={styles.summaryValue}>$900,380</Text>
            <Text style={styles.summaryChange}>+24.7% from last year</Text>
          </View>
          
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Average ROI</Text>
            <Text style={styles.summaryValue}>12.4%</Text>
            <Text style={styles.summaryChange}>+1.8% from last year</Text>
          </View>
        </View>
      </View>

      {/* Property Performance */}
      <View style={styles.performanceSection}>
        <Text style={styles.sectionTitle}>Property Performance</Text>
        <View style={styles.performanceTable}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableHeaderText, { flex: 2 }]}>Property</Text>
            <Text style={styles.tableHeaderText}>Units</Text>
            <Text style={styles.tableHeaderText}>Occupancy</Text>
            <Text style={styles.tableHeaderText}>Monthly Income</Text>
            <Text style={styles.tableHeaderText}>ROI</Text>
          </View>
          
          {[
            { name: '15 Main Street Complex', units: 24, occupancy: 95, income: '$28,800', roi: '14.2%' },
            { name: 'Riverside Apartments', units: 18, occupancy: 88, income: '$21,600', roi: '11.8%' },
            { name: 'Oak Hill Residences', units: 12, occupancy: 92, income: '$16,500', roi: '13.1%' },
            { name: 'Downtown Plaza', units: 16, occupancy: 85, income: '$24,000', roi: '10.5%' },
          ].map((property, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableCellText, { flex: 2, fontWeight: '600' }]}>
                {property.name}
              </Text>
              <Text style={styles.tableCellText}>{property.units}</Text>
              <Text style={styles.tableCellText}>{property.occupancy}%</Text>
              <Text style={styles.tableCellText}>{property.income}</Text>
              <Text style={[styles.tableCellText, { color: '#10B981', fontWeight: '600' }]}>
                {property.roi}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </ScrollView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    <SafeAreaView style={styles.container}>
      {content}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: isDesktop ? 'row' : 'column',
    justifyContent: 'space-between',
    alignItems: isDesktop ? 'center' : 'flex-start',
    paddingHorizontal: 24,
    paddingVertical: 24,
    backgroundColor: '#FFFFFF',
    gap: isDesktop ? 0 : 16,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 4,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
  },
  periodButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  periodButtonActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  periodText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  periodTextActive: {
    color: '#1F2937',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 8,
    gap: 8,
  },
  exportText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3B82F6',
  },
  kpiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 24,
    paddingTop: 24,
    gap: 16,
  },
  kpiGridDesktop: {
    flexWrap: 'nowrap',
  },
  kpiCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    flex: 1,
    minWidth: isDesktop ? 200 : '45%',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  kpiCardDesktop: {
    minWidth: 220,
  },
  kpiHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  kpiIcon: {
    padding: 12,
    borderRadius: 12,
  },
  changeIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  changeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  kpiValue: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1F2937',
    marginBottom: 4,
  },
  kpiTitle: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  chartsSection: {
    paddingHorizontal: 24,
    paddingTop: 32,
    gap: 24,
  },
  chartsSectionDesktop: {
    flexDirection: 'row',
  },
  chartCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  chartCardDesktop: {
    flex: 1,
  },
  chartContainer: {
    minHeight: 300,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  chartLegend: {
    flexDirection: 'row',
    gap: 16,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#6B7280',
  },
  chart: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    height: 200,
    paddingTop: 20,
  },
  chartBar: {
    alignItems: 'center',
    flex: 1,
  },
  barContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
    marginBottom: 8,
  },
  bar: {
    width: 16,
    borderRadius: 4,
  },
  barLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  occupancyChart: {
    gap: 16,
  },
  occupancyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  occupancyProperty: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    width: 120,
  },
  occupancyBarContainer: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
  },
  occupancyBar: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 4,
  },
  occupancyValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1F2937',
    width: 40,
    textAlign: 'right',
  },
  summarySection: {
    paddingHorizontal: 24,
    paddingTop: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 20,
  },
  summaryGrid: {
    gap: 16,
  },
  summaryGridDesktop: {
    flexDirection: 'row',
  },
  summaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    flex: isDesktop ? 1 : undefined,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
    marginTop: 8,
  },
  summaryChange: {
    fontSize: 12,
    color: '#10B981',
    fontWeight: '500',
    marginTop: 4,
  },
  performanceSection: {
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 40,
  },
  performanceTable: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#F9FAFB',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  tableHeaderText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    flex: 1,
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  tableCellText: {
    fontSize: 14,
    color: '#1F2937',
    flex: 1,
    textAlign: 'center',
  },
});