import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Search, MessageCircle, TriangleAlert as <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleCheck as CheckCircle, Clock, Send } from 'lucide-react-native';
import { Platform, Dimensions } from 'react-native';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Messages() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMessage, setSelectedMessage] = useState<number | null>(null);
  const [newMessage, setNewMessage] = useState('');

  const messages = [
    {
      id: 1,
      tenant: '<PERSON>',
      property: '15 Main Street - 6A',
      subject: 'Kitchen faucet is leaking',
      message: 'Hi, the kitchen faucet in my unit has been leaking for the past two days. Could you please send someone to fix it?',
      timestamp: '2 hours ago',
      status: 'urgent',
      unread: true,
    },
    {
      id: 2,
      tenant: '<PERSON> Pincella',
      property: '15 Main Street - 1F',
      subject: 'Thank you for quick repair',
      message: 'Just wanted to thank you for sending the maintenance team so quickly yesterday. The heating is working perfectly now.',
      timestamp: '1 day ago',
      status: 'resolved',
      unread: false,
    },
    {
      id: 3,
      tenant: '<PERSON> Baruch',
      property: '15 Main Street - 2C',
      subject: 'Noise complaint',
      message: 'There has been excessive noise from the unit above me during late hours. Could you please address this with the tenant?',
      timestamp: '2 days ago',
      status: 'pending',
      unread: true,
    },
    {
      id: 4,
      tenant: 'Sarah Johnson',
      property: 'Riverside Apt - 3B',
      subject: 'Parking space assignment',
      message: 'Could you please clarify which parking space is assigned to my unit? I couldn\'t find this information in my lease.',
      timestamp: '3 days ago',
      status: 'pending',
      unread: false,
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'urgent':
        return <AlertTriangle size={16} color="#EF4444" />;
      case 'resolved':
        return <CheckCircle size={16} color="#10B981" />;
      default:
        return <Clock size={16} color="#F59E0B" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'urgent':
        return '#EF4444';
      case 'resolved':
        return '#10B981';
      default:
        return '#F59E0B';
    }
  };

  const MessageDetail = ({ message }: { message: any }) => (
    <View style={styles.messageDetail}>
      <View style={styles.messageHeader}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => setSelectedMessage(null)}
        >
          <Text style={styles.backText}>← Back</Text>
        </TouchableOpacity>
      </View>
      
      <View style={styles.messageContent}>
        <View style={styles.messageInfo}>
          <Text style={styles.messageTenant}>{message.tenant}</Text>
          <Text style={styles.messageProperty}>{message.property}</Text>
          <Text style={styles.messageTimestamp}>{message.timestamp}</Text>
        </View>
        
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(message.status) }]}>
          {getStatusIcon(message.status)}
          <Text style={styles.statusText}>{message.status}</Text>
        </View>
      </View>

      <Text style={styles.messageSubject}>{message.subject}</Text>
      <Text style={styles.messageBody}>{message.message}</Text>

      <View style={styles.replySection}>
        <TextInput
          style={styles.replyInput}
          placeholder="Type your reply..."
          value={newMessage}
          onChangeText={setNewMessage}
          multiline
        />
        <TouchableOpacity style={styles.sendButton}>
          <Send size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (selectedMessage) {
    const message = messages.find(m => m.id === selectedMessage);
    return (
      <SafeAreaView style={styles.container}>
        {message && <MessageDetail message={message} />}
      </SafeAreaView>
    );
  }

  const content = (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Messages</Text>
        <View style={styles.unreadBadge}>
          <Text style={styles.unreadText}>
            {messages.filter(m => m.unread).length} unread
          </Text>
        </View>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#9CA3AF" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search messages..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterTabs}>
        <TouchableOpacity style={[styles.filterTab, styles.activeTab]}>
          <Text style={[styles.filterText, styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={styles.filterText}>Urgent</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={styles.filterText}>Pending</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={styles.filterText}>Resolved</Text>
        </TouchableOpacity>
      </View>

      {/* Messages List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.messagesList}>
          {messages.map((message) => (
            <TouchableOpacity 
              key={message.id} 
              style={[
                styles.messageItem,
                message.unread && styles.unreadMessage
              ]}
              onPress={() => setSelectedMessage(message.id)}
            >
              <View style={styles.messageIcon}>
                <MessageCircle 
                  size={24} 
                  color={message.unread ? '#3B82F6' : '#9CA3AF'} 
                />
              </View>
              
              <View style={styles.messageInfo}>
                <View style={styles.messageRow}>
                  <Text style={[
                    styles.messageTenant,
                    message.unread && styles.unreadText
                  ]}>
                    {message.tenant}
                  </Text>
                  <Text style={styles.messageTimestamp}>{message.timestamp}</Text>
                </View>
                
                <Text style={styles.messageProperty}>{message.property}</Text>
                
                <Text style={[
                  styles.messageSubject,
                  message.unread && styles.unreadText
                ]} numberOfLines={1}>
                  {message.subject}
                </Text>
                
                <Text style={styles.messagePreview} numberOfLines={2}>
                  {message.message}
                </Text>
              </View>

              <View style={styles.messageStatus}>
                <View style={[
                  styles.statusIndicator,
                  { backgroundColor: getStatusColor(message.status) }
                ]}>
                  {getStatusIcon(message.status)}
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    content
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
  },
  unreadBadge: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  unreadText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    gap: 12,
  },
  filterTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  activeTab: {
    backgroundColor: '#3B82F6',
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  activeFilterText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  messagesList: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  messageItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  unreadMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  messageIcon: {
    marginRight: 12,
    paddingTop: 4,
  },
  messageInfo: {
    flex: 1,
  },
  messageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  messageTenant: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  messageProperty: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  messageTimestamp: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  messageSubject: {
    fontSize: 15,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 4,
  },
  messagePreview: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  messageStatus: {
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 4,
  },
  statusIndicator: {
    padding: 6,
    borderRadius: 12,
  },
  messageDetail: {
    flex: 1,
    padding: 20,
  },
  messageHeader: {
    marginBottom: 20,
  },
  backButton: {
    alignSelf: 'flex-start',
  },
  backText: {
    fontSize: 16,
    color: '#3B82F6',
    fontWeight: '500',
  },
  messageContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  statusText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  messageBody: {
    fontSize: 16,
    color: '#1F2937',
    lineHeight: 24,
    marginTop: 16,
    marginBottom: 32,
  },
  replySection: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
    marginTop: 20,
  },
  replyInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    maxHeight: 120,
  },
  sendButton: {
    backgroundColor: '#3B82F6',
    padding: 12,
    borderRadius: 12,
  },
});