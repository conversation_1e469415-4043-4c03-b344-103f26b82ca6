import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput, Modal } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CreditCard, Smartphone, Search, Download, X, CircleCheck as CheckCircle } from 'lucide-react-native';
import { Platform, Dimensions } from 'react-native';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Payments() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');

  const payments = [
    {
      id: 1,
      tenant: '<PERSON>',
      property: '15 Main Street - 6A',
      amount: '$1,500.00',
      date: '2024-11-28',
      status: 'completed',
      method: 'M-Pesa'
    },
    {
      id: 2,
      tenant: '<PERSON>',
      property: '15 Main Street - 1F',
      amount: '$1,200.24',
      date: '2024-11-27',
      status: 'completed',
      method: 'Visa'
    },
    {
      id: 3,
      tenant: 'David Baruch',
      property: '15 Main Street - 2C',
      amount: '$1,100.00',
      date: '2024-11-25',
      status: 'completed',
      method: 'Mastercard'
    },
    {
      id: 4,
      tenant: 'Sarah Johnson',
      property: 'Riverside Apt - 3B',
      amount: '$950.00',
      date: '2024-11-20',
      status: 'pending',
      method: 'M-Pesa'
    },
  ];

  const PaymentModal = () => (
    <Modal visible={showPaymentModal} transparent animationType="slide">
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Make Payment</Text>
            <TouchableOpacity onPress={() => setShowPaymentModal(false)}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.rentInfo}>
            <Text style={styles.rentLabel}>Rent Due</Text>
            <Text style={styles.rentAmount}>$1,200.00</Text>
            <Text style={styles.rentDue}>Due: December 1st, 2024</Text>
          </View>

          <Text style={styles.paymentMethodTitle}>Select Payment Method</Text>
          
          <TouchableOpacity 
            style={[
              styles.paymentMethodCard,
              selectedPaymentMethod === 'mpesa' && styles.paymentMethodSelected
            ]}
            onPress={() => setSelectedPaymentMethod('mpesa')}
          >
            <Smartphone size={32} color="#10B981" />
            <View style={styles.paymentMethodInfo}>
              <Text style={styles.paymentMethodName}>M-Pesa</Text>
              <Text style={styles.paymentMethodDesc}>Mobile money transfer</Text>
            </View>
            {selectedPaymentMethod === 'mpesa' && <CheckCircle size={20} color="#10B981" />}
          </TouchableOpacity>

          <TouchableOpacity 
            style={[
              styles.paymentMethodCard,
              selectedPaymentMethod === 'visa' && styles.paymentMethodSelected
            ]}
            onPress={() => setSelectedPaymentMethod('visa')}
          >
            <CreditCard size={32} color="#1E40AF" />
            <View style={styles.paymentMethodInfo}>
              <Text style={styles.paymentMethodName}>Visa/Mastercard</Text>
              <Text style={styles.paymentMethodDesc}>Credit or debit card</Text>
            </View>
            {selectedPaymentMethod === 'visa' && <CheckCircle size={20} color="#10B981" />}
          </TouchableOpacity>

          {selectedPaymentMethod === 'mpesa' && (
            <View style={styles.paymentForm}>
              <Text style={styles.formLabel}>Phone Number</Text>
              <TextInput
                style={styles.formInput}
                placeholder="+254 7XX XXX XXX"
                keyboardType="phone-pad"
              />
            </View>
          )}

          {selectedPaymentMethod === 'visa' && (
            <View style={styles.paymentForm}>
              <Text style={styles.formLabel}>Card Number</Text>
              <TextInput
                style={styles.formInput}
                placeholder="1234 5678 9012 3456"
                keyboardType="numeric"
              />
              <View style={styles.cardRow}>
                <View style={styles.cardField}>
                  <Text style={styles.formLabel}>Expiry</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="MM/YY"
                  />
                </View>
                <View style={styles.cardField}>
                  <Text style={styles.formLabel}>CVV</Text>
                  <TextInput
                    style={styles.formInput}
                    placeholder="123"
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </View>
          )}

          {selectedPaymentMethod && (
            <TouchableOpacity style={styles.payButton}>
              <Text style={styles.payButtonText}>Pay $1,200.00</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );

  const content = (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Payments</Text>
        <TouchableOpacity 
          style={styles.payNowButton}
          onPress={() => setShowPaymentModal(true)}
        >
          <Text style={styles.payNowText}>Pay Now</Text>
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#9CA3AF" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search payments..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Payment Summary Cards */}
      <View style={styles.summaryCards}>
        <View style={[styles.summaryCard, { backgroundColor: '#10B981' }]}>
          <Text style={styles.summaryLabel}>This Month</Text>
          <Text style={styles.summaryValue}>$28,750</Text>
          <Text style={styles.summarySubtext}>22 payments</Text>
        </View>
        
        <View style={[styles.summaryCard, { backgroundColor: '#EF4444' }]}>
          <Text style={styles.summaryLabel}>Overdue</Text>
          <Text style={styles.summaryValue}>$3,150</Text>
          <Text style={styles.summarySubtext}>3 tenants</Text>
        </View>
      </View>

      {/* Payments List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.paymentsList}>
          <View style={styles.listHeader}>
            <Text style={styles.listTitle}>Recent Transactions</Text>
            <TouchableOpacity style={styles.downloadButton}>
              <Download size={16} color="#3B82F6" />
              <Text style={styles.downloadText}>Export</Text>
            </TouchableOpacity>
          </View>

          {payments.map((payment) => (
            <View key={payment.id} style={styles.paymentItem}>
              <View style={styles.paymentInfo}>
                <Text style={styles.paymentTenant}>{payment.tenant}</Text>
                <Text style={styles.paymentProperty}>{payment.property}</Text>
                <Text style={styles.paymentDate}>{payment.date}</Text>
              </View>
              
              <View style={styles.paymentRight}>
                <Text style={[
                  styles.paymentAmount,
                  { color: payment.status === 'completed' ? '#10B981' : '#F59E0B' }
                ]}>
                  {payment.amount}
                </Text>
                <View style={[
                  styles.statusBadge,
                  { 
                    backgroundColor: payment.status === 'completed' ? '#DCFCE7' : '#FEF3C7' 
                  }
                ]}>
                  <Text style={[
                    styles.statusText,
                    { 
                      color: payment.status === 'completed' ? '#16A34A' : '#D97706' 
                    }
                  ]}>
                    {payment.status}
                  </Text>
                </View>
                <Text style={styles.paymentMethod}>{payment.method}</Text>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      <PaymentModal />
    </SafeAreaView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    content
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
  },
  payNowButton: {
    backgroundColor: '#10B981',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
  },
  payNowText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  summaryCards: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    borderRadius: 16,
    padding: 20,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    fontWeight: '500',
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 4,
  },
  summarySubtext: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.7,
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  paymentsList: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    borderRadius: 16,
    padding: 16,
  },
  listHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  listTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  downloadText: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  paymentInfo: {
    flex: 1,
  },
  paymentTenant: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  paymentProperty: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  paymentDate: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  paymentRight: {
    alignItems: 'flex-end',
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  paymentMethod: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
  },
  rentInfo: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    marginBottom: 24,
  },
  rentLabel: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  rentAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#1F2937',
    marginTop: 4,
  },
  rentDue: {
    fontSize: 14,
    color: '#EF4444',
    marginTop: 4,
  },
  paymentMethodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  paymentMethodCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    marginBottom: 12,
    gap: 16,
  },
  paymentMethodSelected: {
    borderColor: '#10B981',
    backgroundColor: '#F0FDF4',
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  paymentMethodDesc: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  paymentForm: {
    marginTop: 20,
  },
  formLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginBottom: 8,
  },
  formInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#1F2937',
    marginBottom: 16,
  },
  cardRow: {
    flexDirection: 'row',
    gap: 16,
  },
  cardField: {
    flex: 1,
  },
  payButton: {
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  payButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
});