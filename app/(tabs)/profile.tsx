import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Settings, Bell, Shield, CreditCard, CircleHelp as HelpCircle, LogOut, ChevronRight, Chrome as Home, Users, ChartBar as BarChart3 } from 'lucide-react-native';
import { Platform, Dimensions } from 'react-native';
import { useAuthStore } from '@/stores/authStore';
import { router } from 'expo-router';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Profile() {
  const [notifications, setNotifications] = useState(true);
  const [darkMode, setDarkMode] = useState(false);
  const { user, logout } = useAuthStore();
  const userRole = user?.role || 'landlord';

  const handleLogout = () => {
    logout();
    router.replace('/(auth)/login');
  };

  const landlordMenuItems = [
    {
      icon: <Home size={24} color="#6B7280" />,
      title: 'My Properties',
      subtitle: 'Manage your rental properties',
      onPress: () => {},
    },
    {
      icon: <Users size={24} color="#6B7280" />,
      title: 'Tenants',
      subtitle: 'View and manage tenant information',
      onPress: () => {},
    },
    {
      icon: <BarChart3 size={24} color="#6B7280" />,
      title: 'Analytics',
      subtitle: 'View financial reports and insights',
      onPress: () => {},
    },
    {
      icon: <CreditCard size={24} color="#6B7280" />,
      title: 'Payment Settings',
      subtitle: 'Configure payment methods',
      onPress: () => {},
    },
  ];

  const tenantMenuItems = [
    {
      icon: <Home size={24} color="#6B7280" />,
      title: 'My Lease',
      subtitle: 'View lease details and documents',
      onPress: () => {},
    },
    {
      icon: <CreditCard size={24} color="#6B7280" />,
      title: 'Payment Methods',
      subtitle: 'Manage your payment options',
      onPress: () => {},
    },
  ];

  const commonMenuItems = [
    {
      icon: <Bell size={24} color="#6B7280" />,
      title: 'Notifications',
      subtitle: 'Manage notification preferences',
      rightComponent: (
        <Switch
          value={notifications}
          onValueChange={setNotifications}
          trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
          thumbColor={notifications ? '#FFFFFF' : '#F9FAFB'}
        />
      ),
    },
    {
      icon: <Settings size={24} color="#6B7280" />,
      title: 'Dark Mode',
      subtitle: 'Toggle dark mode theme',
      rightComponent: (
        <Switch
          value={darkMode}
          onValueChange={setDarkMode}
          trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
          thumbColor={darkMode ? '#FFFFFF' : '#F9FAFB'}
        />
      ),
    },
    {
      icon: <Shield size={24} color="#6B7280" />,
      title: 'Privacy & Security',
      subtitle: 'Manage your privacy settings',
      onPress: () => {},
    },
    {
      icon: <HelpCircle size={24} color="#6B7280" />,
      title: 'Help & Support',
      subtitle: 'Get help and contact support',
      onPress: () => {},
    },
  ];

  const MenuSection = ({ title, items }: { title: string; items: any[] }) => (
    <View style={styles.menuSection}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <View style={styles.menuItems}>
        {items.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.menuItem,
              index === items.length - 1 && styles.lastMenuItem
            ]}
            onPress={item.onPress}
          >
            <View style={styles.menuItemLeft}>
              <View style={styles.menuIcon}>{item.icon}</View>
              <View style={styles.menuText}>
                <Text style={styles.menuTitle}>{item.title}</Text>
                <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
              </View>
            </View>
            <View style={styles.menuItemRight}>
              {item.rightComponent || <ChevronRight size={20} color="#9CA3AF" />}
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const content = (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileHeader}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=200' }}
              style={styles.profileImage}
            />
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>
              {user ? `${user.firstName} ${user.lastName}` : 'User Name'}
            </Text>
            <Text style={styles.profileRole}>
              {userRole === 'landlord' ? 'Property Manager' : 'Tenant'}
            </Text>
            <Text style={styles.profileEmail}>
              {user?.email || '<EMAIL>'}
            </Text>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <Settings size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Stats Cards for Landlord */}
        {userRole === 'landlord' && (
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>24</Text>
              <Text style={styles.statLabel}>Properties</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>92%</Text>
              <Text style={styles.statLabel}>Occupancy</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statValue}>$28.8k</Text>
              <Text style={styles.statLabel}>Monthly Income</Text>
            </View>
          </View>
        )}

        {/* Tenant Info Card */}
        {userRole === 'tenant' && (
          <View style={styles.tenantCard}>
            <Text style={styles.tenantProperty}>15 Main Street - Apt 2C</Text>
            <Text style={styles.tenantLease}>Lease expires: June 30, 2025</Text>
            <Text style={styles.tenantRent}>Monthly Rent: $1,200.00</Text>
          </View>
        )}

        {/* Menu Sections */}
        <MenuSection 
          title={userRole === 'landlord' ? 'Property Management' : 'My Rental'}
          items={userRole === 'landlord' ? landlordMenuItems : tenantMenuItems}
        />

        <MenuSection title="Settings" items={commonMenuItems} />

        {/* Logout Button */}
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <LogOut size={24} color="#EF4444" />
          <Text style={styles.logoutText}>Log Out</Text>
        </TouchableOpacity>

        {/* App Version */}
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    content
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 20,
  },
  profileImageContainer: {
    marginRight: 16,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1F2937',
  },
  profileRole: {
    fontSize: 16,
    color: '#3B82F6',
    fontWeight: '500',
    marginTop: 2,
  },
  profileEmail: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  editButton: {
    padding: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  statLabel: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  tenantCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginHorizontal: 20,
    marginBottom: 20,
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
  },
  tenantProperty: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  tenantLease: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  tenantRent: {
    fontSize: 16,
    fontWeight: '600',
    color: '#10B981',
    marginTop: 8,
  },
  menuSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
    paddingHorizontal: 20,
  },
  menuItems: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    borderRadius: 12,
  },
  menuItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  lastMenuItem: {
    borderBottomWidth: 0,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    marginRight: 16,
  },
  menuText: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1F2937',
  },
  menuSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  menuItemRight: {
    marginLeft: 12,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF2F2',
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    justifyContent: 'center',
    gap: 12,
    marginBottom: 24,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#EF4444',
  },
  versionText: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
    paddingBottom: 40,
  },
});