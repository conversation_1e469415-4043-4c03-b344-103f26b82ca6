import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Bell, Settings, TrendingUp, Users, DollarSign, Chrome as Home, TriangleAlert as AlertTriangle } from 'lucide-react-native';
import { Platform } from 'react-native';
import { useAuthStore } from '@/stores/authStore';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Dashboard() {
  const { user } = useAuthStore();
  const userRole = user?.role || 'landlord';

  const renderLandlordDashboard = () => (
    <>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Good morning, {user?.firstName || 'John'}</Text>
          <Text style={styles.subtitle}>Welcome back!</Text>
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity style={styles.iconButton}>
            <Bell size={24} color="#6B7280" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Settings size={24} color="#6B7280" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Financial Overview Cards */}
      <View style={styles.cardContainer}>
        <View style={[styles.card, { backgroundColor: '#3B82F6' }]}>
          <View style={styles.cardContent}>
            <DollarSign size={28} color="#FFFFFF" />
            <Text style={styles.cardTitle}>Net Income</Text>
            <Text style={styles.cardValue}>$100,458.11</Text>
            <Text style={styles.cardSubtext}>Last 3 months to date</Text>
          </View>
        </View>

        <View style={[styles.card, { backgroundColor: '#10B981' }]}>
          <View style={styles.cardContent}>
            <Home size={28} color="#FFFFFF" />
            <Text style={styles.cardTitle}>Properties</Text>
            <Text style={styles.cardValue}>24</Text>
            <Text style={styles.cardSubtext}>Active rentals</Text>
          </View>
        </View>
      </View>

      <View style={styles.cardContainer}>
        <View style={[styles.card, { backgroundColor: '#8B5CF6' }]}>
          <View style={styles.cardContent}>
            <Users size={28} color="#FFFFFF" />
            <Text style={styles.cardTitle}>Occupancy</Text>
            <Text style={styles.cardValue}>92%</Text>
            <Text style={styles.cardSubtext}>22 of 24 units</Text>
          </View>
        </View>

        <View style={[styles.card, { backgroundColor: '#F59E0B' }]}>
          <View style={styles.cardContent}>
            <AlertTriangle size={28} color="#FFFFFF" />
            <Text style={styles.cardTitle}>Overdue</Text>
            <Text style={styles.cardValue}>$30,650</Text>
            <Text style={styles.cardSubtext}>From 8 tenants</Text>
          </View>
        </View>
      </View>

      {/* Recent Payments */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Payments Received</Text>
          <TouchableOpacity>
            <Text style={styles.viewAll}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.paymentsList}>
          {[
            { name: 'Maria Brennan', property: '15 Main Street - 6A', amount: '+$1,500.00', days: '2 days ago', color: '#10B981' },
            { name: 'Leo Pincella', property: '15 Main Street - 1F', amount: '+$1,200.24', days: '3 days ago', color: '#10B981' },
            { name: 'David Baruch', property: '15 Main Street - 2C', amount: '+$1,100.00', days: '5 days ago', color: '#10B981' },
          ].map((payment, index) => (
            <View key={index} style={styles.paymentItem}>
              <View style={styles.paymentInfo}>
                <Text style={styles.paymentName}>{payment.name}</Text>
                <Text style={styles.paymentProperty}>{payment.property}</Text>
                <Text style={styles.paymentDays}>{payment.days}</Text>
              </View>
              <Text style={[styles.paymentAmount, { color: payment.color }]}>
                {payment.amount}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {/* Recent Tenant Requests */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Tenant Requests</Text>
          <TouchableOpacity>
            <Text style={styles.viewAll}>View All</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.requestsList}>
          {[
            { issue: 'Fix the lights in elevator #1', property: '15 Main Street - 2F', status: 'Overdue', color: '#EF4444' },
            { issue: 'Fix broken door handle', property: '15 Main Street - 2F', status: 'Not Started', color: '#6B7280' },
            { issue: 'Fix toilet leak', property: '15 Main Street - 2F', status: 'In Progress', color: '#3B82F6' },
          ].map((request, index) => (
            <View key={index} style={styles.requestItem}>
              <View style={styles.requestInfo}>
                <Text style={styles.requestIssue}>{request.issue}</Text>
                <Text style={styles.requestProperty}>{request.property}</Text>
              </View>
              <View style={[styles.statusBadge, { backgroundColor: request.color }]}>
                <Text style={styles.statusText}>{request.status}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    </>
  );

  const renderTenantDashboard = () => (
    <>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Welcome back, {user?.firstName || 'Sarah'}</Text>
          <Text style={styles.subtitle}>15 Main Street - Apt 2C</Text>
        </View>
        <TouchableOpacity style={styles.iconButton}>
          <Bell size={24} color="#6B7280" />
        </TouchableOpacity>
      </View>

      {/* Rent Payment Card */}
      <View style={[styles.card, { backgroundColor: '#3B82F6', marginHorizontal: 20, marginBottom: 20 }]}>
        <View style={styles.cardContent}>
          <Text style={styles.cardTitle}>Current Rent Due</Text>
          <Text style={styles.cardValue}>$1,200.00</Text>
          <Text style={styles.cardSubtext}>Due: December 1st, 2024</Text>
          <TouchableOpacity style={styles.payButton}>
            <Text style={styles.payButtonText}>Pay Now</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton}>
            <DollarSign size={32} color="#3B82F6" />
            <Text style={styles.actionText}>Payment History</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <AlertTriangle size={32} color="#F59E0B" />
            <Text style={styles.actionText}>Report Issue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );

  const content = (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {userRole === 'landlord' ? renderLandlordDashboard() : renderTenantDashboard()}
      </ScrollView>
    </SafeAreaView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    content
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1F2937',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 4,
  },
  headerIcons: {
    flexDirection: 'row',
    gap: 12,
  },
  iconButton: {
    padding: 8,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  cardContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginTop: 20,
    gap: 12,
  },
  card: {
    flex: 1,
    borderRadius: 16,
    padding: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  cardContent: {
    alignItems: 'flex-start',
  },
  cardTitle: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.8,
    marginTop: 8,
    fontWeight: '500',
  },
  cardValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    marginTop: 4,
  },
  cardSubtext: {
    fontSize: 12,
    color: '#FFFFFF',
    opacity: 0.7,
    marginTop: 4,
  },
  section: {
    marginTop: 32,
    paddingHorizontal: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  viewAll: {
    fontSize: 14,
    color: '#3B82F6',
    fontWeight: '500',
  },
  paymentsList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  paymentInfo: {
    flex: 1,
  },
  paymentName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  paymentProperty: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  paymentDays: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
  requestsList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
  },
  requestItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  requestInfo: {
    flex: 1,
  },
  requestIssue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  requestProperty: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 20,
  },
  statusText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  payButton: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  payButtonText: {
    color: '#3B82F6',
    fontSize: 16,
    fontWeight: '600',
  },
  quickActions: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 16,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginTop: 8,
    textAlign: 'center',
  },
});