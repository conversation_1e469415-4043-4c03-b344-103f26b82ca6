import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, TextInput } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Search, MapPin, Users, DollarSign, Eye } from 'lucide-react-native';
import { Platform, Dimensions } from 'react-native';
import DesktopLayout from '@/components/DesktopLayout';

const { width } = Dimensions.get('window');
const isDesktop = Platform.OS === 'web' && width > 768;

export default function Properties() {
  const [searchQuery, setSearchQuery] = useState('');

  const properties = [
    {
      id: 1,
      name: '15 Main Street Complex',
      address: '15 Main Street, Downtown',
      units: 24,
      occupied: 22,
      monthlyIncome: '$28,800',
      image: 'https://images.pexels.com/photos/106399/pexels-photo-106399.jpeg?auto=compress&cs=tinysrgb&w=800',
      status: 'active'
    },
    {
      id: 2,
      name: 'Riverside Apartments',
      address: '890 River Drive, Westside',
      units: 18,
      occupied: 16,
      monthlyIncome: '$21,600',
      image: 'https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg?auto=compress&cs=tinysrgb&w=800',
      status: 'active'
    },
    {
      id: 3,
      name: 'Oak Hill Residences',
      address: '456 Oak Street, Suburbs',
      units: 12,
      occupied: 11,
      monthlyIncome: '$16,500',
      image: 'https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800',
      status: 'active'
    },
  ];

  const content = (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Properties</Text>
        <TouchableOpacity style={styles.addButton}>
          <Plus size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBox}>
          <Search size={20} color="#9CA3AF" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search properties..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9CA3AF"
          />
        </View>
      </View>

      {/* Properties List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.propertiesList}>
          {properties.map((property) => (
            <TouchableOpacity key={property.id} style={styles.propertyCard}>
              <Image source={{ uri: property.image }} style={styles.propertyImage} />
              <View style={styles.propertyInfo}>
                <View style={styles.propertyHeader}>
                  <Text style={styles.propertyName}>{property.name}</Text>
                  <TouchableOpacity style={styles.viewButton}>
                    <Eye size={16} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
                
                <View style={styles.addressContainer}>
                  <MapPin size={14} color="#6B7280" />
                  <Text style={styles.propertyAddress}>{property.address}</Text>
                </View>

                <View style={styles.propertyStats}>
                  <View style={styles.stat}>
                    <Users size={16} color="#6B7280" />
                    <Text style={styles.statText}>
                      {property.occupied}/{property.units} units
                    </Text>
                  </View>
                  
                  <View style={styles.stat}>
                    <DollarSign size={16} color="#10B981" />
                    <Text style={[styles.statText, { color: '#10B981' }]}>
                      {property.monthlyIncome}
                    </Text>
                  </View>
                </View>

                <View style={styles.occupancyBar}>
                  <View 
                    style={[
                      styles.occupancyFill, 
                      { width: `${(property.occupied / property.units) * 100}%` }
                    ]} 
                  />
                </View>
                <Text style={styles.occupancyText}>
                  {Math.round((property.occupied / property.units) * 100)}% Occupied
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Add Property Button */}
        <TouchableOpacity style={styles.addPropertyCard}>
          <Plus size={48} color="#9CA3AF" />
          <Text style={styles.addPropertyText}>Add New Property</Text>
          <Text style={styles.addPropertySubtext}>Start managing a new rental property</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );

  return isDesktop ? (
    <DesktopLayout>{content}</DesktopLayout>
  ) : (
    content
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#FFFFFF',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1F2937',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    padding: 12,
    borderRadius: 12,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  scrollView: {
    flex: 1,
  },
  propertiesList: {
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  propertyCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  propertyImage: {
    width: '100%',
    height: 200,
  },
  propertyInfo: {
    padding: 16,
  },
  propertyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  propertyName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  viewButton: {
    padding: 8,
  },
  addressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 6,
  },
  propertyAddress: {
    fontSize: 14,
    color: '#6B7280',
  },
  propertyStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  stat: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  occupancyBar: {
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
    marginBottom: 8,
  },
  occupancyFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 3,
  },
  occupancyText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  addPropertyCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 20,
    marginVertical: 20,
    padding: 40,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderStyle: 'dashed',
  },
  addPropertyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 16,
  },
  addPropertySubtext: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
    textAlign: 'center',
  },
});